import React from 'react'
import DeathStarLoader from './DeathStarLoader'
import { STAR_WARS_MESSAGES } from '@constants/index'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  message?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  message 
}) => {
  const sizeMap = {
    small: 40,
    medium: 60,
    large: 80
  }

  const spinnerSize = sizeMap[size]
  
  // Use Star Wars themed loading message if none provided
  const loadingMessage = message || STAR_WARS_MESSAGES.LOADING[
    Math.floor(Math.random() * STAR_WARS_MESSAGES.LOADING.length)
  ]

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        width: '100%',
      }}
    >
      <DeathStarLoader 
        size={spinnerSize}
        message={loadingMessage}
        className="loading-spinner"
      />
    </div>
  )
}

export default LoadingSpinner
